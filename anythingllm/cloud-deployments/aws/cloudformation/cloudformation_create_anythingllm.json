{"AWSTemplateFormatVersion": "2010-09-09", "Description": "Create a stack that runs AnythingLLM on a single instance", "Parameters": {"InstanceType": {"Description": "EC2 instance type", "Type": "String", "Default": "t3.small"}, "InstanceVolume": {"Description": "Storage size of disk on Instance in GB", "Type": "Number", "Default": 10, "MinValue": 4}}, "Resources": {"AnythingLLMInstance": {"Type": "AWS::EC2::Instance", "Properties": {"ImageId": {"Fn::FindInMap": ["Region2AMI", {"Ref": "AWS::Region"}, "AMI"]}, "InstanceType": {"Ref": "InstanceType"}, "SecurityGroupIds": [{"Ref": "AnythingLLMInstanceSecurityGroup"}], "BlockDeviceMappings": [{"DeviceName": {"Fn::FindInMap": ["Region2AMI", {"Ref": "AWS::Region"}, "RootDeviceName"]}, "Ebs": {"VolumeSize": {"Ref": "InstanceVolume"}}}], "UserData": {"Fn::Base64": {"Fn::Join": ["", ["Content-Type: multipart/mixed; boundary=\"//\"\n", "MIME-Version: 1.0\n", "\n", "--//\n", "Content-Type: text/cloud-config; charset=\"us-ascii\"\n", "MIME-Version: 1.0\n", "Content-Transfer-Encoding: 7bit\n", "Content-Disposition: attachment; filename=\"cloud-config.txt\"\n", "\n", "\n", "#cloud-config\n", "cloud_final_modules:\n", "- [scripts-user, once-per-instance]\n", "\n", "\n", "--//\n", "Content-Type: text/x-shellscript; charset=\"us-ascii\"\n", "MIME-Version: 1.0\n", "Content-Transfer-Encoding: 7bit\n", "Content-Disposition: attachment; filename=\"userdata.txt\"\n", "\n", "\n", "#!/bin/bash\n", "# check output of userdata script with sudo tail -f /var/log/cloud-init-output.log\n", "sudo yum install docker iptables -y\n", "sudo iptables -A OUTPUT -m owner ! --uid-owner root -d *************** -j DROP\n", "sudo systemctl enable docker\n", "sudo systemctl start docker\n", "mkdir -p /home/<USER>/anythingllm\n", "touch /home/<USER>/anythingllm/.env\n", "sudo chown ec2-user:ec2-user -R /home/<USER>/anythingllm\n", "docker pull mintplexlabs/anythingllm\n", "docker run -d -p 3001:3001 --cap-add SYS_ADMIN -v /home/<USER>/anythingllm:/app/server/storage -v /home/<USER>/anythingllm/.env:/app/server/.env -e STORAGE_DIR=\"/app/server/storage\" mintplexlabs/anythingllm\n", "echo \"Container ID: $(sudo docker ps --latest --quiet)\"\n", "export ONLINE=$(curl -Is http://localhost:3001/api/ping | head -n 1|cut -d$' ' -f2)\n", "echo \"Health check: $ONLINE\"\n", "echo \"Setup complete! AnythingLLM instance is now online!\"\n", "\n", "--//--\n"]]}}}}, "AnythingLLMInstanceSecurityGroup": {"Type": "AWS::EC2::SecurityGroup", "Properties": {"GroupDescription": "AnythingLLM Instance Security Group", "SecurityGroupIngress": [{"IpProtocol": "tcp", "FromPort": "22", "ToPort": "22", "CidrIp": "0.0.0.0/0"}, {"IpProtocol": "tcp", "FromPort": "3001", "ToPort": "3001", "CidrIp": "0.0.0.0/0"}, {"IpProtocol": "tcp", "FromPort": "3001", "ToPort": "3001", "CidrIpv6": "::/0"}]}}}, "Outputs": {"ServerIp": {"Description": "IP address of the AnythingLLM instance", "Value": {"Fn::GetAtt": ["AnythingLLMInstance", "PublicIp"]}}, "ServerURL": {"Description": "URL of the AnythingLLM server", "Value": {"Fn::Join": ["", ["http://", {"Fn::GetAtt": ["AnythingLLMInstance", "PublicIp"]}, ":3001"]]}}}, "Mappings": {"Region2AMI": {"ap-south-1": {"AMI": "ami-0e6329e222e662a52", "RootDeviceName": "/dev/xvda"}, "eu-north-1": {"AMI": "ami-08c308b1bb265e927", "RootDeviceName": "/dev/xvda"}, "eu-west-3": {"AMI": "ami-069d1ea6bc64443f0", "RootDeviceName": "/dev/xvda"}, "eu-west-2": {"AMI": "ami-06a566ca43e14780d", "RootDeviceName": "/dev/xvda"}, "eu-west-1": {"AMI": "ami-0a8dc52684ee2fee2", "RootDeviceName": "/dev/xvda"}, "ap-northeast-3": {"AMI": "ami-0c8a89b455fae8513", "RootDeviceName": "/dev/xvda"}, "ap-northeast-2": {"AMI": "ami-0ff56409a6e8ea2a0", "RootDeviceName": "/dev/xvda"}, "ap-northeast-1": {"AMI": "ami-0ab0bbbd329f565e6", "RootDeviceName": "/dev/xvda"}, "ca-central-1": {"AMI": "ami-033c256a10931f206", "RootDeviceName": "/dev/xvda"}, "sa-east-1": {"AMI": "ami-0dabf4dab6b183eef", "RootDeviceName": "/dev/xvda"}, "ap-southeast-1": {"AMI": "ami-0dc5785603ad4ff54", "RootDeviceName": "/dev/xvda"}, "ap-southeast-2": {"AMI": "ami-0c5d61202c3b9c33e", "RootDeviceName": "/dev/xvda"}, "eu-central-1": {"AMI": "ami-004359656ecac6a95", "RootDeviceName": "/dev/xvda"}, "us-east-1": {"AMI": "ami-0cff7528ff583bf9a", "RootDeviceName": "/dev/xvda"}, "us-east-2": {"AMI": "ami-02238ac43d6385ab3", "RootDeviceName": "/dev/xvda"}, "us-west-1": {"AMI": "ami-01163e76c844a2129", "RootDeviceName": "/dev/xvda"}, "us-west-2": {"AMI": "ami-0ceecbb0f30a902a6", "RootDeviceName": "/dev/xvda"}}}}