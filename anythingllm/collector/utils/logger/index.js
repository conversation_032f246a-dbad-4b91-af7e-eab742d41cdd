const winston = require("winston");
const path = require("path");
const fs = require("fs");

class Logger {
  logger = console;
  static _instance;
  constructor() {
    if (Logger._instance) return Logger._instance;
    this.logger = this.getWinstonLogger();
    Logger._instance = this;
  }

  getWinstonLogger() {
    // 确保日志目录存在
    const logDir = path.join(__dirname, "../../../logs/collector");
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    // 创建文件格式（不包含颜色代码）
    const fileFormat = winston.format.combine(
      winston.format.timestamp({ format: "YYYY-MM-DD HH:mm:ss" }),
      winston.format.errors({ stack: true }),
      winston.format.printf(({ timestamp, level, message, service, origin = "" }) => {
        return `${timestamp} [${service}]${origin ? `[${origin}]` : ""} ${level.toUpperCase()}: ${message}`;
      })
    );

    // 创建控制台格式（包含颜色）
    const consoleFormat = winston.format.combine(
      winston.format.colorize(),
      winston.format.timestamp({ format: "HH:mm:ss" }),
      winston.format.printf(({ timestamp, level, message, service, origin = "" }) => {
        return `${timestamp} \x1b[36m[${service}]\x1b[0m${origin ? `\x1b[33m[${origin}]\x1b[0m` : ""} ${level}: ${message}`;
      })
    );

    const logger = winston.createLogger({
      level: "info",
      defaultMeta: { service: "collector" },
      transports: [
        // 控制台输出（开发和生产环境都保留）
        new winston.transports.Console({
          format: consoleFormat,
        }),
        // 综合日志文件
        new winston.transports.File({
          filename: path.join(logDir, "combined.log"),
          format: fileFormat,
          maxsize: 10 * 1024 * 1024, // 10MB
          maxFiles: 5,
        }),
        // 错误日志文件
        new winston.transports.File({
          filename: path.join(logDir, "error.log"),
          level: "error",
          format: fileFormat,
          maxsize: 10 * 1024 * 1024, // 10MB
          maxFiles: 5,
        }),
        // 按日期归档的日志文件
        new winston.transports.File({
          filename: path.join(logDir, `collector-${new Date().toISOString().slice(0, 10)}.log`),
          format: fileFormat,
          maxsize: 50 * 1024 * 1024, // 50MB
          maxFiles: 30,
        }),
      ],
    });

    function formatArgs(args) {
      return args
        .map((arg) => {
          if (arg instanceof Error) {
            return arg.stack; // If argument is an Error object, return its stack trace
          } else if (typeof arg === "object") {
            return JSON.stringify(arg); // Convert objects to JSON string
          } else {
            return arg; // Otherwise, return as-is
          }
        })
        .join(" ");
    }

    console.log = function (...args) {
      logger.info(formatArgs(args));
    };
    console.error = function (...args) {
      logger.error(formatArgs(args));
    };
    console.info = function (...args) {
      logger.warn(formatArgs(args));
    };
    return logger;
  }
}

/**
 * Sets and overrides Console methods for logging when called.
 * This is a singleton method and will not create multiple loggers.
 * @returns {winston.Logger | console} - instantiated logger interface.
 */
function setLogger() {
  return new Logger().logger;
}
module.exports = setLogger;
