import React, { useState, useEffect, useCallback } from "react";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import Workspace from "@/models/workspace";
import ManageWorkspace, {
  useManageWorkspaceModal,
} from "../../Modals/ManageWorkspace";
import paths from "@/utils/paths";
import { useParams, useNavigate, useMatch } from "react-router-dom";
import { GearSix, UploadSimple, DotsSixVertical } from "@phosphor-icons/react";
import useUser from "@/hooks/useUser";
import ThreadContainer from "./ThreadContainer";
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd";
import showToast from "@/utils/toast";

// 工作区操作按钮组件
const WorkspaceActions = ({
  workspace,
  isActive,
  isInWorkspaceSettings,
  slug,
  onUploadClick,
  onSettingsClick
}) => {
  const handleUploadClick = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    onUploadClick(workspace);
  }, [workspace, onUploadClick]);

  const handleSettingsClick = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    onSettingsClick(workspace, isInWorkspaceSettings, slug);
  }, [workspace, isInWorkspaceSettings, slug, onSettingsClick]);

  return (
    <div
      className={`flex items-center gap-x-[2px] transition-opacity duration-200 ${
        isActive ? "opacity-100" : "opacity-0 group-hover:opacity-100"
      }`}
    >
      <button
        type="button"
        onClick={handleUploadClick}
        className="border-none rounded-md flex items-center justify-center ml-auto p-[2px] hover:bg-[#646768] text-[#A7A8A9] hover:text-white"
      >
        <UploadSimple className="h-[20px] w-[20px]" />
      </button>
      <button
        onClick={handleSettingsClick}
        className="rounded-md flex items-center justify-center text-[#A7A8A9] hover:text-white ml-auto p-[2px] hover:bg-[#646768]"
        aria-label="General appearance settings"
      >
        <GearSix
          color={
            isInWorkspaceSettings && workspace.slug === slug
              ? "#46C8FF"
              : undefined
          }
          className="h-[20px] w-[20px]"
        />
      </button>
    </div>
  );
};

// 工作区内容组件
const WorkspaceContent = ({
  workspace,
  isActive,
  isInWorkspaceSettings,
  slug,
  user,
  provided,
  onUploadClick,
  onSettingsClick
}) => {
  const baseClasses = `
    transition-all duration-[200ms]
    flex flex-grow w-[75%] gap-x-2 py-[6px] pl-[4px] pr-[6px] rounded-[4px] text-white justify-start items-center
  `;

  const activeClasses = `
    ${baseClasses}
    bg-theme-sidebar-item-selected font-bold
    hover:bg-theme-sidebar-subitem-hover hover:font-bold
    light:outline-2 light:outline light:outline-blue-400 light:outline-offset-[-2px]
  `;

  const inactiveClasses = `
    ${baseClasses}
    bg-theme-sidebar-item-default
    hover:bg-theme-sidebar-subitem-hover hover:font-bold
  `;

  const contentJSX = (
    <div className="flex flex-row items-center justify-between w-full">
      {user?.role !== "default" && (
        <div
          {...provided.dragHandleProps}
          className="cursor-grab mr-[3px]"
        >
          <DotsSixVertical
            size={20}
            color="var(--theme-sidebar-item-workspace-active)"
            weight="bold"
          />
        </div>
      )}
      <div className="flex items-center flex-grow space-x-2 overflow-hidden">
        <div className="w-[130px] overflow-hidden">
          <p
            className={`
              text-[14px] leading-loose whitespace-nowrap overflow-hidden text-white
              ${isActive ? "font-bold" : "font-medium"} truncate
              w-full group-hover:w-[130px] group-hover:font-bold group-hover:duration-200
            `}
          >
            {workspace.name}
          </p>
        </div>
      </div>
      {user?.role !== "default" && (
        <WorkspaceActions
          workspace={workspace}
          isActive={isActive}
          isInWorkspaceSettings={isInWorkspaceSettings}
          slug={slug}
          onUploadClick={onUploadClick}
          onSettingsClick={onSettingsClick}
        />
      )}
    </div>
  );

  if (isActive) {
    return (
      <div aria-current="page" className={activeClasses}>
        {contentJSX}
      </div>
    );
  }

  return (
    <a href={paths.workspace.chat(workspace.slug)} className={inactiveClasses}>
      {contentJSX}
    </a>
  );
};

// 单个工作区项组件
const WorkspaceItem = ({
  workspace,
  index,
  isActive,
  user,
  isInWorkspaceSettings,
  slug,
  onUploadClick,
  onSettingsClick
}) => {
  return (
    <Draggable
      key={workspace.id}
      draggableId={workspace.id.toString()}
      index={index}
      isDragDisabled={user?.role === "default"}
    >
      {(provided, snapshot) => (
        <li
          ref={provided.innerRef}
          {...provided.draggableProps}
          className={`flex flex-col w-full group ${
            snapshot.isDragging ? "opacity-50" : ""
          }`}
        >
          <div className="flex items-center justify-between gap-x-2">
            <WorkspaceContent
              workspace={workspace}
              isActive={isActive}
              isInWorkspaceSettings={isInWorkspaceSettings}
              slug={slug}
              user={user}
              provided={provided}
              onUploadClick={onUploadClick}
              onSettingsClick={onSettingsClick}
            />
          </div>
          {isActive && (
            <ThreadContainer
              workspace={workspace}
              isActive={isActive}
            />
          )}
        </li>
      )}
    </Draggable>
  );
};

export default function ActiveWorkspaces() {
  const navigate = useNavigate();
  const { slug } = useParams();
  const [loading, setLoading] = useState(true);
  const [workspaces, setWorkspaces] = useState([]);
  const [selectedWs, setSelectedWs] = useState(null);
  const { showing, showModal, hideModal } = useManageWorkspaceModal();
  const { user } = useUser();
  const isInWorkspaceSettings = !!useMatch("/workspace/:slug/settings/:tab");

  // 事件处理函数
  const handleUploadClick = useCallback((workspace) => {
    setSelectedWs(workspace);
    showModal();
  }, [showModal]);

  const handleSettingsClick = useCallback((workspace, isInWorkspaceSettings, slug) => {
    if (isInWorkspaceSettings && workspace.slug === slug) {
      navigate(paths.workspace.chat(workspace.slug));
      return;
    }
    navigate(paths.workspace.settings.generalAppearance(workspace.slug));
  }, [navigate, isInWorkspaceSettings, slug]);

  const reorderWorkspaces = useCallback((startIndex, endIndex) => {
    const reorderedWorkspaces = Array.from(workspaces);
    const [removed] = reorderedWorkspaces.splice(startIndex, 1);
    reorderedWorkspaces.splice(endIndex, 0, removed);
    setWorkspaces(reorderedWorkspaces);

    const success = Workspace.storeWorkspaceOrder(
      reorderedWorkspaces.map((w) => w.id)
    );
    if (!success) {
      showToast("Failed to reorder workspaces", "error");
      Workspace.all().then((workspaces) => setWorkspaces(workspaces));
    }
  }, [workspaces]);

  const onDragEnd = useCallback((result) => {
    if (!result.destination) return;
    reorderWorkspaces(result.source.index, result.destination.index);
  }, [reorderWorkspaces]);

  useEffect(() => {
    async function getWorkspaces() {
      const workspaces = await Workspace.all();
      setLoading(false);
      setWorkspaces(Workspace.orderWorkspaces(workspaces));
    }
    getWorkspaces();
  }, []);

  if (loading) {
    return (
      <Skeleton
        height={40}
        width="100%"
        count={5}
        baseColor="var(--theme-sidebar-item-default)"
        highlightColor="var(--theme-sidebar-item-hover)"
        enableAnimation={true}
        className="my-1"
      />
    );
  }

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <Droppable droppableId="workspaces">
        {(provided) => (
          <ul
            aria-label="Workspaces"
            className="flex flex-col gap-y-2"
            ref={provided.innerRef}
            {...provided.droppableProps}
          >
            {workspaces.map((workspace, index) => {
              const isActive = workspace.slug === slug;
              return (
                <WorkspaceItem
                  key={workspace.id}
                  workspace={workspace}
                  index={index}
                  isActive={isActive}
                  user={user}
                  isInWorkspaceSettings={isInWorkspaceSettings}
                  slug={slug}
                  onUploadClick={handleUploadClick}
                  onSettingsClick={handleSettingsClick}
                />
              );
            })}
            {provided.placeholder}
            {showing && (
              <ManageWorkspace
                hideModal={hideModal}
                providedSlug={selectedWs ? selectedWs.slug : null}
              />
            )}
          </ul>
        )}
      </Droppable>
    </DragDropContext>
  );
}
